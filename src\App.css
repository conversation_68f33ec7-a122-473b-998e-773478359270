/* Global Styles */
.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #fafafa 0%, #f0f2f5 100%);
}

main {
  flex: 1;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* Enhanced Container Variants */
.container-sm {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.container-lg {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.container-fluid {
  width: 100%;
  padding: 0 var(--spacing-lg);
}

/* Enhanced Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: 12px var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-align: center;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  user-select: none;
  line-height: 1;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, var(--primary-dark) 0%, #6a4c93 100%);
}

.btn-secondary {
  background: var(--light-gray);
  color: var(--text-light);
  border: 2px solid var(--medium-gray);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--medium-gray);
  border-color: var(--dark-gray);
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  position: relative;
  z-index: 1;
}

.btn-outline::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: var(--primary-color);
  transition: width var(--transition-normal);
  z-index: -1;
}

.btn-outline:hover {
  color: var(--white);
}

.btn-outline:hover::after {
  width: 100%;
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.btn-success:hover {
  background: linear-gradient(135deg, #218838 0%, #1ea080 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color) 0%, #e74c3c 100%);
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.btn-danger:hover {
  background: linear-gradient(135deg, #c82333 0%, #c0392b 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning-color) 0%, #f39c12 100%);
  color: var(--text-dark);
  box-shadow: var(--shadow-sm);
}

.btn-warning:hover {
  background: linear-gradient(135deg, #e0a800 0%, #d68910 100%);
  transform: translateY(-2px);
}

.btn-info {
  background: linear-gradient(135deg, var(--info-color) 0%, #3498db 100%);
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.btn-info:hover {
  background: linear-gradient(135deg, #138496 0%, #2980b9 100%);
  transform: translateY(-2px);
}

/* Button Sizes */
.btn-sm {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-base);
}

.btn-xl {
  padding: var(--spacing-lg) var(--spacing-xxl);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-lg);
}

.btn-full {
  width: 100%;
}

.btn-large {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-base);
}

/* Button States */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  pointer-events: none;
}

.btn:disabled::before {
  display: none;
}

.btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* Button Groups */
.btn-group {
  display: inline-flex;
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.btn-group .btn {
  border-radius: 0;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-group .btn:first-child {
  border-radius: var(--radius-md) 0 0 var(--radius-md);
}

.btn-group .btn:last-child {
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
  border-right: none;
}

.btn-group .btn:only-child {
  border-radius: var(--radius-md);
  border-right: none;
}

/* Enhanced Header Styles */
.header {
  background: var(--white);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1px solid var(--medium-gray);
}

.header .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  min-height: 70px;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  text-decoration: none;
  color: var(--text-dark);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  transition: transform var(--transition-normal);
}

.logo:hover {
  transform: scale(1.05);
}

.logo-icon {
  width: 36px;
  height: 36px;
  color: var(--primary-color);
  filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.2));
  transition: all var(--transition-normal);
}

.logo:hover .logo-icon {
  color: var(--primary-dark);
  transform: rotate(10deg);
}

.nav {
  display: flex;
  gap: var(--spacing-xl);
  align-items: center;
}

.nav-link {
  text-decoration: none;
  color: var(--text-light);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-base);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  transition: all var(--transition-normal);
  transform: translateX(-50%);
}

.nav-link:hover {
  color: var(--primary-color);
  background: rgba(102, 126, 234, 0.05);
}

.nav-link:hover::before {
  width: 80%;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.cart-link {
  position: relative;
  text-decoration: none;
  color: var(--text-light);
  transition: all var(--transition-normal);
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  background: var(--light-gray);
  border: 2px solid transparent;
}

.cart-link:hover {
  color: var(--primary-color);
  background: var(--white);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.cart-icon {
  width: 24px;
  height: 24px;
}

.cart-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: linear-gradient(135deg, var(--danger-color), #e74c3c);
  color: var(--white);
  border-radius: 50%;
  width: 22px;
  height: 22px;
  font-size: var(--font-size-xs);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  border: 2px solid var(--white);
  box-shadow: var(--shadow-sm);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.user-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--light-gray);
  border-radius: var(--radius-lg);
  border: 2px solid transparent;
  transition: all var(--transition-normal);
}

.user-menu:hover {
  background: var(--white);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.user-name {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-light);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.user-icon {
  width: 20px;
  height: 20px;
  color: var(--primary-color);
}

.logout-btn {
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.logout-btn:hover {
  background: var(--danger-color);
  color: var(--white);
  transform: scale(1.1);
}

.logout-icon {
  width: 16px;
  height: 16px;
}

.admin-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  text-decoration: none;
  color: var(--text-light);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--light-gray);
  border-radius: var(--radius-md);
  border: 2px solid transparent;
  transition: all var(--transition-normal);
}

.admin-link:hover {
  color: var(--primary-color);
  background: var(--white);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Mobile Navigation */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  color: var(--text-dark);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
}

.mobile-menu-btn:hover {
  background: var(--light-gray);
}

@media (max-width: 768px) {
  .nav {
    display: none;
  }
  
  .mobile-menu-btn {
    display: block;
  }
  
  .header-actions {
    gap: var(--spacing-md);
  }
  
  .user-menu {
    padding: var(--spacing-xs) var(--spacing-sm);
  }
  
  .user-name {
    display: none;
  }
}

/* Enhanced Home Page Styles */
.home {
  padding: 0;
  overflow-x: hidden;
}

.hero {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: var(--white);
  padding: 120px 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
  opacity: 0.3;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.hero-content {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
  line-height: var(--line-height-tight);
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-xxl);
  opacity: 0.95;
  line-height: var(--line-height-relaxed);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-lg);
  justify-content: center;
  flex-wrap: wrap;
  animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-image {
  margin-top: var(--spacing-xxl);
  animation: fadeInUp 1s ease-out 0.6s both;
}

.jewelry-showcase {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 240px;
  height: 240px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  margin: 0 auto;
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  animation: rotate 20s linear infinite;
  position: relative;
}

.jewelry-showcase::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), transparent, rgba(255, 255, 255, 0.3));
  animation: rotate 10s linear infinite reverse;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.showcase-icon {
  width: 100px;
  height: 100px;
  color: var(--white);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
  z-index: 1;
  position: relative;
}

.features {
  padding: 120px 0;
  background: var(--white);
  position: relative;
}

.features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
}

.section-title {
  text-align: center;
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xxl);
  color: var(--text-dark);
  position: relative;
  display: inline-block;
  width: 100%;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 2px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-xxl);
}

.feature-card {
  text-align: center;
  padding: var(--spacing-xxl) var(--spacing-lg);
  border-radius: var(--radius-lg);
  background: var(--white);
  border: 1px solid var(--medium-gray);
  transition: all var(--transition-slow);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  transform: scaleX(0);
  transition: transform var(--transition-slow);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-icon {
  width: 64px;
  height: 64px;
  color: var(--primary-color);
  margin: 0 auto var(--spacing-lg);
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--white);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.feature-card h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-md);
  color: var(--text-dark);
  transition: color var(--transition-normal);
}

.feature-card:hover h3 {
  color: var(--primary-color);
}

.feature-card p {
  color: var(--text-light);
  line-height: var(--line-height-relaxed);
  font-size: var(--font-size-base);
}

.business-types {
  padding: 100px 0;
  background: #f8f9fa;
}

.business-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
}

.business-card {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.business-card:hover {
  transform: translateY(-5px);
}

.business-card h3 {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}

.business-card p {
  color: #666;
  margin-bottom: 24px;
  line-height: 1.6;
}

.business-card ul {
  list-style: none;
  margin-bottom: 32px;
}

.business-card li {
  padding: 8px 0;
  color: #666;
  position: relative;
  padding-left: 20px;
}

.business-card li:before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #28a745;
  font-weight: 600;
}

.cta {
  padding: 100px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.cta h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
}

.cta p {
  font-size: 1.25rem;
  margin-bottom: 40px;
  opacity: 0.9;
}

/* Enhanced Products Page Styles */
.products {
  padding: var(--spacing-xxl) 0;
  background: var(--light-gray);
  min-height: 100vh;
}

.products-header {
  text-align: center;
  margin-bottom: var(--spacing-xxl);
}

.products-header h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-dark);
  margin-bottom: var(--spacing-md);
}

.b2b-notice {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: var(--white);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  margin: var(--spacing-lg) auto;
  max-width: 600px;
  text-align: center;
  box-shadow: var(--shadow-md);
}

.b2b-notice p {
  margin: 0;
  font-weight: var(--font-weight-medium);
}

.category-filter {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xxl);
  flex-wrap: wrap;
}

.category-btn {
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px solid var(--medium-gray);
  background: var(--white);
  color: var(--text-light);
  border-radius: 25px;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.category-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.category-btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: var(--spacing-xl);
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* Enhanced Product Cards */
.product-card {
  background: var(--white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-slow);
  border: 2px solid transparent;
  position: relative;
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-lg);
}

.product-card.b2b-card {
  border-color: var(--warning-color);
  background: linear-gradient(145deg, var(--white) 0%, #fffbf0 100%);
}

.product-card.b2b-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.2);
}

.product-card.b2c-card:hover {
  border-color: var(--primary-color);
}

.product-image {
  position: relative;
  height: 280px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.product-card:hover .product-image img {
  transform: scale(1.08);
}

/* Product Badges */
.out-of-stock {
  position: absolute;
  top: var(--spacing-md);
  left: var(--spacing-md);
  background: var(--danger-color);
  color: var(--white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  z-index: 2;
}

.savings-badge {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: linear-gradient(135deg, var(--success-color), #20c997);
  color: var(--white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  z-index: 2;
  animation: pulse 2s infinite;
}

.sku-badge {
  position: absolute;
  bottom: var(--spacing-md);
  left: var(--spacing-md);
  background: rgba(0, 0, 0, 0.7);
  color: var(--white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  z-index: 2;
}

/* Product Info */
.product-info {
  padding: var(--spacing-lg);
}

.product-header {
  margin-bottom: var(--spacing-md);
}

.product-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin-bottom: var(--spacing-xs);
  line-height: var(--line-height-tight);
}

.product-category {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  background: rgba(102, 126, 234, 0.1);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  display: inline-block;
}

.product-description {
  color: var(--text-light);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-md);
}

.product-material {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm);
  background: var(--light-gray);
  border-radius: var(--radius-sm);
}

/* Enhanced Pricing */
.product-pricing {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--light-gray);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--primary-color);
}

.price-section {
  margin-bottom: var(--spacing-md);
}

.main-price {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.current-price {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.price-label {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  font-weight: var(--font-weight-medium);
  background: var(--white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
}

.price-comparison {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.original-price {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  text-decoration: line-through;
}

.savings-text {
  font-size: var(--font-size-sm);
  color: var(--success-color);
  font-weight: var(--font-weight-medium);
}

/* B2B Info */
.b2b-info {
  background: rgba(255, 193, 7, 0.1);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  border: 1px solid var(--warning-color);
}

.min-quantity-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.min-qty-label {
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

.min-qty-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  color: var(--warning-color);
}

.bulk-savings {
  text-align: center;
}

.bulk-text {
  font-size: var(--font-size-xs);
  color: var(--success-color);
  font-weight: var(--font-weight-medium);
}

/* Product Actions */
.product-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.quantity-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.quantity-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
}

.quantity-selector {
  display: flex;
  align-items: center;
  border: 2px solid var(--medium-gray);
  border-radius: var(--radius-md);
  overflow: hidden;
  width: fit-content;
}

.quantity-btn {
  background: var(--light-gray);
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn:hover:not(:disabled) {
  background: var(--primary-color);
  color: var(--white);
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity {
  padding: var(--spacing-sm) var(--spacing-md);
  font-weight: var(--font-weight-semibold);
  min-width: 50px;
  text-align: center;
  background: var(--white);
}

.quantity-warning {
  font-size: var(--font-size-xs);
  color: var(--danger-color);
  font-weight: var(--font-weight-medium);
}

.total-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--white);
  border-radius: var(--radius-md);
  border: 2px solid var(--primary-color);
}

.total-label {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  font-weight: var(--font-weight-medium);
}

.total-amount {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.add-to-cart-btn {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-base);
}

.add-to-cart-btn:hover:not(:disabled) {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.add-to-cart-btn.b2b-btn {
  background: linear-gradient(135deg, var(--warning-color), #f39c12);
}

.add-to-cart-btn.b2b-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #e0a800, #d68910);
}

.add-to-cart-btn:disabled {
  background: var(--medium-gray);
  cursor: not-allowed;
  transform: none;
}

.b2b-promotion {
  text-align: center;
  padding: var(--spacing-sm);
  background: rgba(102, 126, 234, 0.05);
  border-radius: var(--radius-sm);
  border: 1px dashed var(--primary-color);
}

.b2b-promotion p {
  margin: 0;
  font-size: var(--font-size-xs);
  color: var(--text-light);
}

.b2b-promotion a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.b2b-promotion a:hover {
    text-decoration: underline;
  }

/* Enhanced Reseller Application Styles */
.password-input {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input input {
  flex: 1;
  padding-right: 50px;
}

.password-toggle {
  position: absolute;
  right: var(--spacing-sm);
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-normal);
}

.password-toggle:hover {
  color: var(--primary-color);
  background: rgba(102, 126, 234, 0.1);
}

/* Error Message */
.error-message {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid var(--danger-color);
  color: var(--danger-color);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.error-message p {
  margin: 0;
  font-weight: var(--font-weight-medium);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin: var(--spacing-xl) 0;
}

.btn {
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: all var(--transition-normal);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 2px solid transparent;
}

.btn-primary {
  background: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-secondary:hover {
  background: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
}

/* Enhanced Success Animation */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.success-icon {
  animation: bounce 1s ease-in-out;
}

/* Cart Page Styles */
.cart {
  padding: 40px 0;
  min-height: 80vh;
}

.empty-cart {
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-cart-content {
  text-align: center;
  max-width: 400px;
}

.empty-cart-icon {
  width: 80px;
  height: 80px;
  color: #ccc;
  margin: 0 auto 24px;
}

.empty-cart-content h2 {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}

.empty-cart-content p {
  color: #666;
  margin-bottom: 32px;
  line-height: 1.6;
}

.cart-header {
  margin-bottom: 40px;
}

.cart-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: #333;
}

.cart-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 40px;
}

.cart-items {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.cart-item {
  display: grid;
  grid-template-columns: 100px 1fr auto auto auto auto;
  gap: 20px;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #eee;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  overflow: hidden;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-details h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.item-description {
  color: #666;
  font-size: 0.875rem;
  margin-bottom: 4px;
}

.item-category {
  color: #999;
  font-size: 0.75rem;
  text-transform: uppercase;
  font-weight: 500;
}

.item-price {
  text-align: center;
}

.item-price .price {
  font-size: 1.125rem;
  font-weight: 600;
  color: #667eea;
}

.price-type {
  font-size: 0.75rem;
  color: #28a745;
  font-weight: 500;
  display: block;
  margin-top: 4px;
}

.item-quantity {
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-total {
  text-align: center;
}

.total-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: #333;
}

.remove-btn {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.remove-btn:hover {
  background: #f8f9fa;
}

.cart-summary {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.summary-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.summary-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}

.summary-row:last-child {
  border-bottom: none;
}

.summary-row.total {
  font-size: 1.125rem;
  font-weight: 700;
  color: #333;
  border-top: 2px solid #eee;
  margin-top: 12px;
  padding-top: 20px;
}

.summary-row.discount {
  color: #28a745;
  font-weight: 500;
}

.cart-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 20px;
}

.b2b-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.b2b-info h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.b2b-info ul {
  list-style: none;
}

.b2b-info li {
  padding: 4px 0;
  color: #666;
  font-size: 0.875rem;
  position: relative;
  padding-left: 16px;
}

.b2b-info li:before {
  content: '•';
  position: absolute;
  left: 0;
  color: #667eea;
  font-weight: 600;
}

/* Checkout Page Styles */
.checkout {
  padding: 40px 0;
  min-height: 80vh;
}

.checkout h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 40px;
  color: #333;
  text-align: center;
}

.checkout-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 40px;
}

.checkout-form {
  background: white;
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.form-section {
  margin-bottom: 32px;
}

.form-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 2px solid #f8f9fa;
  padding-bottom: 8px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.order-summary {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}

.summary-item:last-child {
  border-bottom: none;
}

.item-info {
  flex: 1;
}

.item-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.item-qty {
  font-size: 0.875rem;
  color: #666;
}

.item-total {
  font-weight: 600;
  color: #667eea;
}

.summary-total {
  font-size: 1.25rem;
  font-weight: 700;
  color: #333;
  text-align: center;
  padding: 16px 0;
  border-top: 2px solid #eee;
  margin-top: 12px;
}

.process-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.process-info h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.process-info ol {
  padding-left: 20px;
}

.process-info li {
  padding: 4px 0;
  color: #666;
  line-height: 1.5;
}

.check-icon {
  width: 16px;
  height: 16px;
  color: #28a745;
  margin-right: 8px;
}

/* Login Pages Styles */
.b2b-login,
.admin-login {
  padding: 60px 0;
  min-height: 80vh;
  background: #f8f9fa;
}

.login-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 60px;
  align-items: start;
}

.login-info {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.info-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.info-icon {
  width: 40px;
  height: 40px;
  color: #667eea;
}

.info-header h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #333;
}

.info-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}

.info-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 24px;
}

.b2b-benefits,
.admin-features {
  margin-bottom: 24px;
}

.b2b-benefits h4,
.admin-features h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.b2b-benefits ul,
.admin-features ul {
  list-style: none;
}

.b2b-benefits li,
.admin-features li {
  padding: 6px 0;
  color: #666;
  position: relative;
  padding-left: 20px;
}

.b2b-benefits li:before,
.admin-features li:before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #28a745;
  font-weight: 600;
}

.demo-credentials {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.demo-credentials h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.demo-account {
  margin-bottom: 12px;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.demo-account:last-child {
  margin-bottom: 0;
}

.security-note {
  font-size: 0.75rem;
  color: #dc3545;
  margin-top: 8px;
}

.login-form-container {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.login-form {
  width: 100%;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: #333;
}

.form-header p {
  color: #666;
}

.form-icon {
  width: 48px;
  height: 48px;
  color: #667eea;
  margin: 0 auto 16px;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
}

.label-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: #666;
}

.password-input {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
}

.form-footer {
  text-align: center;
  margin-top: 24px;
}

.form-footer p {
  color: #666;
  margin-bottom: 8px;
}

.link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.link:hover {
  text-decoration: underline;
}

.security-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  margin-top: 16px;
}

.notice-icon {
  width: 16px;
  height: 16px;
  color: #667eea;
}

.security-notice p {
  margin: 0;
  font-size: 0.875rem;
  color: #666;
}

/* Reseller Application Styles */
.reseller-application {
  padding: 40px 0;
  min-height: 80vh;
}

.application-header {
  text-align: center;
  margin-bottom: 60px;
}

.application-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: #333;
}

.application-header p {
  font-size: 1.125rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.benefits-section {
  margin-bottom: 60px;
}

.benefits-section h2 {
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 40px;
  color: #333;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.benefit-item {
  text-align: center;
  padding: 30px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.benefit-item:hover {
  transform: translateY(-5px);
}

.benefit-icon {
  width: 48px;
  height: 48px;
  color: #667eea;
  margin: 0 auto 16px;
}

.benefit-item h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.benefit-item p {
  color: #666;
  line-height: 1.5;
}

.application-form {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
}

.form-actions {
  text-align: center;
  margin-top: 32px;
}

.success-message {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
  background: white;
  padding: 60px 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.success-icon {
  width: 80px;
  height: 80px;
  color: #28a745;
  margin: 0 auto 24px;
}

.success-message h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: #333;
}

.success-message p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 32px;
}

.next-steps {
  text-align: left;
  margin-bottom: 32px;
}

.next-steps h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}

.next-steps ol {
  padding-left: 20px;
}

.next-steps li {
  padding: 6px 0;
  color: #666;
  line-height: 1.5;
}

.contact-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  font-size: 0.875rem;
  color: #666;
}

/* Dashboard Styles */
.b2b-dashboard,
.admin-dashboard {
  padding: 40px 0;
  min-height: 80vh;
}

.dashboard-header {
  margin-bottom: 40px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.welcome-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.company-icon,
.admin-icon {
  width: 48px;
  height: 48px;
  color: #667eea;
}

.welcome-section h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 4px;
  color: #333;
}

.welcome-section p {
  color: #666;
}

.quick-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-icon .icon {
  width: 24px;
  height: 24px;
  color: white;
}

.stat-content h3 {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 4px;
  color: #333;
}

.stat-content p {
  color: #666;
  font-size: 0.875rem;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.dashboard-section {
  background: white;
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
}

.view-all-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.view-all-link:hover {
  text-decoration: underline;
}

.account-info {
  background: #f8f9fa;
  padding: 24px;
  border-radius: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.info-icon {
  width: 20px;
  height: 20px;
  color: #667eea;
}

.info-item label {
  font-weight: 500;
  color: #666;
  font-size: 0.875rem;
  display: block;
  margin-bottom: 2px;
}

.info-item span {
  font-weight: 600;
  color: #333;
}

.status-active {
  color: #28a745 !important;
}

.orders-table {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #dee2e6;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid #eee;
  align-items: center;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background: #f8f9fa;
}

.order-id {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #667eea;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-delivered {
  background: #d4edda;
  color: #155724;
}

.status-shipped {
  background: #d1ecf1;
  color: #0c5460;
}

.status-processing {
  background: #fff3cd;
  color: #856404;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.benefit-card {
  text-align: center;
  padding: 24px;
  background: #f8f9fa;
  border-radius: 8px;
}

.benefit-icon {
  width: 40px;
  height: 40px;
  color: #667eea;
  margin: 0 auto 12px;
}

.benefit-card h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.benefit-card p {
  color: #666;
  font-size: 0.875rem;
  line-height: 1.5;
}

.quick-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.quick-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  text-decoration: none;
  color: #333;
  transition: background 0.2s ease;
}

.quick-link:hover {
  background: #e9ecef;
}

.link-icon {
  width: 20px;
  height: 20px;
  color: #667eea;
}

/* Admin Dashboard Specific Styles */
.admin-tabs {
  display: flex;
  gap: 4px;
  margin-bottom: 32px;
  background: white;
  padding: 4px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: none;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  color: #666;
  transition: all 0.2s ease;
  position: relative;
}

.tab:hover {
  background: #f8f9fa;
}

.tab.active {
  background: #667eea;
  color: white;
}

.tab-icon {
  width: 16px;
  height: 16px;
}

.notification-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background: #dc3545;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.tab-content {
  min-height: 400px;
}

.overview-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.recent-activity {
  background: white;
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.recent-activity h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 24px;
  color: #333;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.activity-icon {
  width: 20px;
  height: 20px;
  color: #667eea;
}

.activity-time {
  margin-left: auto;
  font-size: 0.875rem;
  color: #999;
}

.products-content,
.categories-content,
.resellers-content {
  background: white;
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.search-bar {
  position: relative;
  max-width: 300px;
  flex: 1;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: #666;
}

.search-bar input {
  width: 100%;
  padding: 12px 16px 12px 40px;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  font-size: 14px;
}

.search-bar input:focus {
  outline: none;
  border-color: #667eea;
}

.products-table {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
}

.products-table .table-header {
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr 1fr;
}

.products-table .table-row {
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr 1fr;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-thumb {
  width: 50px;
  height: 50px;
  border-radius: 6px;
  object-fit: cover;
}

.product-name {
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.product-desc {
  font-size: 0.75rem;
  color: #666;
  display: block;
}

.status.in-stock {
  background: #d4edda;
  color: #155724;
}

.status.out-of-stock {
  background: #f8d7da;
  color: #721c24;
}

.actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.action-btn.edit {
  color: #667eea;
}

.action-btn.edit:hover {
  background: #e7f1ff;
}

.action-btn.delete {
  color: #dc3545;
}

.action-btn.delete:hover {
  background: #f8d7da;
}

.add-category {
  display: flex;
  gap: 12px;
  align-items: center;
}

.add-category input {
  padding: 12px 16px;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  font-size: 14px;
  min-width: 200px;
}

.add-category input:focus {
  outline: none;
  border-color: #667eea;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.category-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.category-card:hover {
  border-color: #667eea;
}

.category-name {
  font-weight: 600;
  color: #333;
}

.delete-category {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.delete-category:hover {
  background: #f8d7da;
}

.applications-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.application-card {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 24px;
  background: #f8f9fa;
}

.application-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.company-info h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 4px;
  color: #333;
}

.company-info p {
  color: #666;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-approved {
  background: #d4edda;
  color: #155724;
}

.status-rejected {
  background: #f8d7da;
  color: #721c24;
}

.application-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
}

.detail-row span:first-child {
  font-weight: 500;
  color: #666;
}

.detail-row span:last-child {
  color: #333;
}

.application-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.close-btn:hover {
  background: #f8f9fa;
}

.modal-form {
  padding: 32px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #eee;
}

/* Image Upload Component Styles */
.image-upload {
  width: 100%;
}

.upload-area {
  border: 2px dashed var(--medium-gray);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  overflow: hidden;
  background: var(--light-gray);
}

.upload-area:hover {
  border-color: var(--primary-color);
  background: rgba(102, 126, 234, 0.05);
}

.image-preview {
  position: relative;
  width: 100%;
  height: 200px;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.image-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.action-btn {
  background: var(--white);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.change-btn {
  color: var(--primary-color);
}

.change-btn:hover {
  background: var(--primary-color);
  color: var(--white);
}

.remove-btn {
  color: var(--danger-color);
}

.remove-btn:hover {
  background: var(--danger-color);
  color: var(--white);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxl);
  text-align: center;
  min-height: 200px;
}

.upload-icon {
  width: 48px;
  height: 48px;
  color: var(--text-muted);
  margin-bottom: var(--spacing-md);
}

.upload-text {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-dark);
  margin-bottom: var(--spacing-sm);
}

.upload-hint {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.upload-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--white);
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.upload-error {
  margin-top: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--danger-color);
  color: var(--white);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
}

/* Loading and Error States */
.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--spacing-xxl) var(--spacing-lg);
  min-height: 400px;
}

.loading-state .spinner,
.error-state .error-icon {
  margin-bottom: var(--spacing-lg);
  color: var(--primary-color);
}

.error-state .error-icon {
  color: var(--danger-color);
}

.loading-state h2,
.error-state h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  color: var(--text-dark);
}

.loading-state p,
.error-state p {
  font-size: var(--font-size-lg);
  color: var(--text-light);
  margin-bottom: var(--spacing-lg);
}

.no-products {
  text-align: center;
  padding: var(--spacing-xxl);
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  margin: var(--spacing-lg) 0;
}

.no-products h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-md);
  color: var(--text-dark);
}

.no-products p {
  font-size: var(--font-size-base);
  color: var(--text-light);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .features-grid,
  .business-grid {
    grid-template-columns: 1fr;
  }
  
  .cart-content,
  .checkout-content,
  .login-content {
    grid-template-columns: 1fr;
  }
  
  .cart-item {
    grid-template-columns: 80px 1fr;
    gap: 12px;
  }
  
  .item-price,
  .item-quantity,
  .item-total {
    grid-column: 1 / -1;
    justify-self: start;
    margin-top: 8px;
  }
  
  .remove-btn {
    grid-column: 1 / -1;
    justify-self: end;
    margin-top: 8px;
  }
  
  .nav {
    display: none;
  }
  
  .header .container {
    flex-wrap: wrap;
    gap: 16px;
  }
  
  .admin-tabs {
    flex-wrap: wrap;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .products-table .table-header,
  .products-table .table-row {
    grid-template-columns: 1fr;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .content-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-bar {
    max-width: none;
  }
}
