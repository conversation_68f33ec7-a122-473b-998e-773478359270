/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Modern Color Palette */
  --primary-color: #7C616C;
  --primary-light: #D1B1CB;
  --primary-dark: #5A4A52;
  --secondary-color: #DDCAD9;
  --accent-color: #E5E9EC;
  --light-mint: #EAFDF8;
  --success-color: #4CAF50;
  --danger-color: #F44336;
  --warning-color: #FF9800;
  --info-color: #2196F3;
  
  /* Neutral Colors */
  --white: #ffffff;
  --light-gray: #EAFDF8;
  --medium-gray: #E5E9EC;
  --dark-gray: #7C616C;
  --text-dark: #3A3A3A;
  --text-light: #6B6B6B;
  --text-muted: #9E9E9E;
  
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  
  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 20px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 25px rgba(124, 97, 108, 0.3);
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.75rem;
  --font-size-4xl: 2rem;
  --font-size-5xl: 2.5rem;
  
  /* Font Weights */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Line Heights */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.6;
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  line-height: var(--line-height-normal);
  color: var(--text-dark);
  background: linear-gradient(135deg, var(--light-mint) 0%, var(--accent-color) 100%);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--text-dark);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin: 0;
  line-height: var(--line-height-relaxed);
}

/* Links */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-normal);
}

a:hover {
  color: var(--primary-dark);
}

/* Lists */
ul, ol {
  margin: 0;
  padding: 0;
}

li {
  list-style: none;
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Form Elements */
input, textarea, select, button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

input, textarea, select {
  border: 2px solid var(--medium-gray);
  border-radius: var(--radius-md);
  padding: 12px 16px;
  transition: border-color var(--transition-normal);
  background-color: var(--white);
  color: var(--text-dark);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(124, 97, 108, 0.15);
}

input::placeholder, textarea::placeholder {
  color: var(--text-muted);
}

/* Button Reset */
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  transition: all var(--transition-normal);
  border-radius: var(--radius-md);
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-weight-normal { font-weight: var(--font-weight-normal); }
.font-weight-medium { font-weight: var(--font-weight-medium); }
.font-weight-semibold { font-weight: var(--font-weight-semibold); }
.font-weight-bold { font-weight: var(--font-weight-bold); }

.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-danger { color: var(--danger-color); }
.text-warning { color: var(--warning-color); }
.text-muted { color: var(--text-muted); }

/* Focus Styles */
*:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Selection Styles */
::selection {
  background-color: var(--primary-color);
  color: var(--white);
}

::-moz-selection {
  background-color: var(--primary-color);
  color: var(--white);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
  background: var(--medium-gray);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--dark-gray);
}

/* Print Styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a, a:visited {
    text-decoration: underline;
  }
  
  a[href]:after {
    content: " (" attr(href) ")";
  }
  
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  
  .no-print {
    display: none !important;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
